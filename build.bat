@echo off
echo Building Yes/No Animal Guessing Game...

REM Create directories
if not exist "build" mkdir build
if not exist "build\classes" mkdir build\classes

REM Compile Java files
echo Compiling Java source files...
javac -d build\classes src\main\java\*.java

if %ERRORLEVEL% NEQ 0 (
    echo Compilation failed!
    pause
    exit /b 1
)

REM Create JAR file
echo Creating JAR file...
cd build\classes
jar cfe ..\..\YesNoGame.jar YesNoGame *.class
cd ..\..

if %ERRORLEVEL% NEQ 0 (
    echo JAR creation failed!
    pause
    exit /b 1
)

echo Build successful! YesNoGame.jar created.
echo Run the game with: java -jar YesNoGame.jar
pause
