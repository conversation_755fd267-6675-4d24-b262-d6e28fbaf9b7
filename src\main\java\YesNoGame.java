import java.util.Scanner;

/**
 * Main class for the Yes/No Animal Guessing Game
 * The computer tries to guess what animal you're thinking of by asking yes/no questions
 */
public class YesNoGame {
    public static void main(String[] args) {
        System.out.println("=== Welcome to the Yes/No Animal Guessing Game! ===");
        System.out.println("Think of an animal, and I'll try to guess it by asking yes/no questions.");
        System.out.println("Answer with 'yes', 'y', 'no', or 'n'");
        System.out.println("Type 'quit' to exit the game.\n");
        
        AnimalGuessingGame game = new AnimalGuessingGame();
        Scanner scanner = new Scanner(System.in);
        
        while (true) {
            System.out.println("\n--- New Game ---");
            System.out.println("Think of an animal and press Enter when ready (or type 'quit' to exit):");
            
            String input = scanner.nextLine().trim().toLowerCase();
            if (input.equals("quit") || input.equals("q")) {
                break;
            }
            
            game.playGame(scanner);
            
            System.out.println("\nWould you like to play again? (yes/no)");
            String playAgain = scanner.nextLine().trim().toLowerCase();
            if (playAgain.equals("no") || playAgain.equals("n")) {
                break;
            }
        }
        
        System.out.println("\nThanks for playing! The game has learned from our session.");
        scanner.close();
    }
}
