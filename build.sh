#!/bin/bash

echo "Building Yes/No Animal Guessing Game..."

# Create directories
mkdir -p build/classes

# Compile Java files
echo "Compiling Java source files..."
javac -d build/classes src/main/java/*.java

if [ $? -ne 0 ]; then
    echo "Compilation failed!"
    exit 1
fi

# Create JAR file
echo "Creating JAR file..."
cd build/classes

# Try to find jar command
JAR_CMD="jar"
if ! command -v jar &> /dev/null; then
    # Try common Java installation paths
    if [ -f "/usr/bin/jar" ]; then
        JAR_CMD="/usr/bin/jar"
    elif [ -f "/usr/local/bin/jar" ]; then
        JAR_CMD="/usr/local/bin/jar"
    elif [ -f "/c/Program Files/Java/jdk-24/bin/jar.exe" ]; then
        JAR_CMD="/c/Program Files/Java/jdk-24/bin/jar.exe"
    else
        echo "jar command not found. Please ensure Java JDK is installed."
        exit 1
    fi
fi

$JAR_CMD cfe ../../YesNoGame.jar YesNoGame *.class
cd ../..

if [ $? -ne 0 ]; then
    echo "JAR creation failed!"
    exit 1
fi

echo "Build successful! YesNoGame.jar created."
echo "Run the game with: java -jar YesNoGame.jar"
