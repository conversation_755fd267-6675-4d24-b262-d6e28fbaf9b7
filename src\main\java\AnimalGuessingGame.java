import java.io.*;
import java.util.Scanner;

/**
 * Core game logic for the Animal Guessing Game
 * Manages the decision tree and handles game flow
 */
public class AnimalGuessingGame {
    private static final String SAVE_FILE = "animal_knowledge.ser";
    private DecisionNode root;
    
    public AnimalGuessingGame() {
        loadKnowledge();
    }
    
    /**
     * Play one round of the guessing game
     */
    public void playGame(Scanner scanner) {
        DecisionNode current = root;
        
        // Traverse the decision tree
        while (!current.isLeaf()) {
            System.out.println(current.getContent());
            String answer = getYesNoAnswer(scanner);
            
            if (answer.equals("yes")) {
                current = current.getYesNode();
            } else {
                current = current.getNoNode();
            }
        }
        
        // We've reached a leaf node (animal guess)
        System.out.println("Is your animal a " + current.getContent() + "?");
        String answer = getYesNoAnswer(scanner);
        
        if (answer.equals("yes")) {
            System.out.println("Great! I guessed correctly! 🎉");
        } else {
            // Learn from the mistake
            learnNewAnimal(scanner, current);
        }
        
        saveKnowledge();
    }
    
    /**
     * Learn a new animal when the guess is wrong
     */
    private void learnNewAnimal(Scanner scanner, DecisionNode wrongGuess) {
        System.out.println("I was wrong! What animal were you thinking of?");
        String newAnimal = scanner.nextLine().trim();
        
        System.out.println("What question would distinguish a " + newAnimal + 
                         " from a " + wrongGuess.getContent() + "?");
        System.out.println("(Please phrase as a yes/no question)");
        String newQuestion = scanner.nextLine().trim();
        
        System.out.println("For a " + newAnimal + ", would the answer to '" + 
                         newQuestion + "' be yes or no?");
        String answerForNewAnimal = getYesNoAnswer(scanner);
        
        // Create new nodes
        DecisionNode newAnimalNode = new DecisionNode(newAnimal);
        DecisionNode oldAnimalNode = new DecisionNode(wrongGuess.getContent());
        
        // Update the current node to be a question
        wrongGuess.setContent(newQuestion);
        wrongGuess.setLeaf(false);
        
        if (answerForNewAnimal.equals("yes")) {
            wrongGuess.setYesNode(newAnimalNode);
            wrongGuess.setNoNode(oldAnimalNode);
        } else {
            wrongGuess.setYesNode(oldAnimalNode);
            wrongGuess.setNoNode(newAnimalNode);
        }
        
        System.out.println("Thanks! I've learned something new! 📚");
    }
    
    /**
     * Get a yes/no answer from the user
     */
    private String getYesNoAnswer(Scanner scanner) {
        while (true) {
            String answer = scanner.nextLine().trim().toLowerCase();
            if (answer.equals("yes") || answer.equals("y")) {
                return "yes";
            } else if (answer.equals("no") || answer.equals("n")) {
                return "no";
            } else {
                System.out.println("Please answer 'yes' or 'no':");
            }
        }
    }
    
    /**
     * Load the decision tree from file
     */
    private void loadKnowledge() {
        try {
            File file = new File(SAVE_FILE);
            if (file.exists()) {
                FileInputStream fis = new FileInputStream(file);
                ObjectInputStream ois = new ObjectInputStream(fis);
                root = (DecisionNode) ois.readObject();
                ois.close();
                fis.close();
                System.out.println("Loaded existing knowledge base.");
            } else {
                // Initialize with a simple starting tree
                initializeDefaultTree();
                System.out.println("Starting with basic knowledge base.");
            }
        } catch (Exception e) {
            System.out.println("Error loading knowledge base, starting fresh: " + e.getMessage());
            initializeDefaultTree();
        }
    }
    
    /**
     * Save the decision tree to file
     */
    private void saveKnowledge() {
        try {
            FileOutputStream fos = new FileOutputStream(SAVE_FILE);
            ObjectOutputStream oos = new ObjectOutputStream(fos);
            oos.writeObject(root);
            oos.close();
            fos.close();
        } catch (Exception e) {
            System.out.println("Error saving knowledge base: " + e.getMessage());
        }
    }
    
    /**
     * Initialize with a basic decision tree
     */
    private void initializeDefaultTree() {
        // Start with a simple tree: Does it live in water? -> Fish vs Dog
        DecisionNode fishNode = new DecisionNode("fish");
        DecisionNode dogNode = new DecisionNode("dog");
        root = new DecisionNode("Does it live in water?", fishNode, dogNode);
    }
}
