# Yes/No Animal Guessing Game

A classic AI game where the computer tries to guess what animal you're thinking of by asking yes/no questions. The game learns from each interaction and builds a decision tree of knowledge.

## How to Play

1. Think of any animal
2. Answer the computer's yes/no questions
3. If the computer guesses wrong, teach it about your animal
4. The game gets smarter with each round!

## How to Build and Run

### Building the Game
Run the build script to compile and create the JAR file:
```
build.bat
```

### Running the Game
After building, run the game with:
```
RunGame.bat
```

Or directly with:
```
java -jar YesNoGame.jar
```

## Features

- **Learning AI**: The game learns from mistakes and gets better over time
- **Persistent Knowledge**: Game knowledge is saved between sessions
- **Interactive**: Simple yes/no interface
- **Expandable**: Starts with basic knowledge but grows with use

## Game Mechanics

The game uses a binary decision tree where:
- Each internal node represents a yes/no question
- Each leaf node represents an animal guess
- When the game guesses wrong, it learns by:
  1. Asking what the correct animal was
  2. Asking for a distinguishing question
  3. Rebuilding the tree with new knowledge

## Files

- `src/main/java/YesNoGame.java` - Main entry point
- `src/main/java/AnimalGuessingGame.java` - Core game logic
- `src/main/java/DecisionNode.java` - Decision tree node structure
- `animal_knowledge.ser` - Saved knowledge base (created after first run)
- `build.bat` - Build script
- `RunGame.bat` - Run script

## Example Session

```
=== Welcome to the Yes/No Animal Guessing Game! ===
Think of an animal, and I'll try to guess it by asking yes/no questions.

--- New Game ---
Think of an animal and press Enter when ready:

Does it live in water?
> no

Is your animal a dog?
> no

I was wrong! What animal were you thinking of?
> cat

What question would distinguish a cat from a dog?
> Does it meow?

For a cat, would the answer to 'Does it meow?' be yes or no?
> yes

Thanks! I've learned something new! 📚
```

Enjoy playing and teaching the AI!
