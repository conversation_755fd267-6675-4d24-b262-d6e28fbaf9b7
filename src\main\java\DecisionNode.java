import java.io.Serializable;

/**
 * Represents a node in the decision tree for the animal guessing game
 * Can be either a question node (with yes/no branches) or a leaf node (animal guess)
 */
public class DecisionNode implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private String content;  // Either a question or an animal name
    private DecisionNode yesNode;  // Branch for "yes" answer
    private DecisionNode noNode;   // Branch for "no" answer
    private boolean isLeaf;        // True if this is an animal (leaf), false if it's a question
    
    /**
     * Constructor for leaf node (animal)
     */
    public DecisionNode(String animal) {
        this.content = animal;
        this.isLeaf = true;
        this.yesNode = null;
        this.noNode = null;
    }
    
    /**
     * Constructor for question node
     */
    public DecisionNode(String question, DecisionNode yesNode, DecisionNode noNode) {
        this.content = question;
        this.isLeaf = false;
        this.yesNode = yesNode;
        this.noNode = noNode;
    }
    
    // Getters
    public String getContent() {
        return content;
    }
    
    public DecisionNode getYesNode() {
        return yesNode;
    }
    
    public DecisionNode getNoNode() {
        return noNode;
    }
    
    public boolean isLeaf() {
        return isLeaf;
    }
    
    // Setters
    public void setContent(String content) {
        this.content = content;
    }
    
    public void setYesNode(DecisionNode yesNode) {
        this.yesNode = yesNode;
    }
    
    public void setNoNode(DecisionNode noNode) {
        this.noNode = noNode;
    }
    
    public void setLeaf(boolean leaf) {
        this.isLeaf = leaf;
    }
    
    @Override
    public String toString() {
        if (isLeaf) {
            return "Animal: " + content;
        } else {
            return "Question: " + content;
        }
    }
}
